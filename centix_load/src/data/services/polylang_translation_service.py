import logging
from typing import Dict, List, Optional

from src.data.models.woocommerce.category import Category as WCCategory
from src.data.woocommerce_category_datasource import WoocommerceCategoryDatasource
from src.domain.models.proposition import Category, CategoryTranslation
from src.shared.enum import LanguageEnum


class PolylangTranslationService:
    """
    Service for managing Polylang translations of WooCommerce categories.
    Handles creating and linking translated versions of categories.
    """

    def __init__(self, category_datasource: WoocommerceCategoryDatasource):
        self._category_datasource = category_datasource
        self._logger = logging.getLogger(__name__)

    def get_translation_for_language(self, category: Category, language: LanguageEnum) -> Optional[str]:
        """
        Get the translation label for a specific language from a category.
        Falls back to English if the requested language is not available.
        """
        # First try to get the requested language
        for translation in category.translations:
            if translation.language == language:
                return translation.label
        
        # Fallback to English
        for translation in category.translations:
            if translation.language == LanguageEnum.EN:
                return translation.label
        
        # If no English translation, return the first available translation
        if category.translations:
            return category.translations[0].label
        
        return None

    def get_available_languages(self, category: Category) -> List[LanguageEnum]:
        """
        Get all available languages for a category.
        """
        return [translation.language for translation in category.translations]

    def create_translated_categories(
        self, 
        category: Category, 
        parent_id: int, 
        used_slugs_by_parent: Dict[int, set[str]], 
        existing_by_guid: Dict
    ) -> Dict[LanguageEnum, WCCategory]:
        """
        Create translated versions of a category for all available languages.
        Returns a dictionary mapping language codes to created WCCategory objects.
        """
        created_categories = {}
        translation_links = {}
        
        # Get all available languages for this category
        available_languages = self.get_available_languages(category)
        
        # Create categories for each language
        for language in available_languages:
            label = self.get_translation_for_language(category, language)
            if not label:
                self._logger.warning(f"No translation found for category {category.guid} in language {language}")
                continue
                
            # Generate unique slug for this language
            slug = self._generate_unique_slug_for_language(category, language, used_slugs_by_parent[parent_id])
            used_slugs_by_parent[parent_id].add(slug)
            
            # Create the category payload
            payload = {
                "name": label,
                "slug": slug,
                "parent": parent_id,
                "guid": category.guid,
                "menu_order": category.sort_index,
            }
            
            self._logger.info(f"Creating category {category.guid} in language {language.value}")
            
            # Create the category in the specific language
            wc_category = self._category_datasource.create(payload, language)
            created_categories[language] = wc_category
            translation_links[language] = wc_category.id
            
            # Update the existing_by_guid mapping
            existing_by_guid[wc_category.guid] = wc_category
        
        # Now link all translations together
        self._link_translations(created_categories, translation_links)
        
        return created_categories

    def update_translated_categories(
        self, 
        existing_wc: WCCategory, 
        category: Category, 
        parent_id: int, 
        existing_by_guid: Dict
    ) -> None:
        """
        Update translated versions of a category for all available languages.
        """
        available_languages = self.get_available_languages(category)
        
        for language in available_languages:
            label = self.get_translation_for_language(category, language)
            if not label:
                continue
                
            # Check if update is needed
            if (
                existing_wc.name != label
                or existing_wc.parent_id != parent_id
                or existing_wc.menu_order != category.sort_index
            ):
                payload = {
                    "name": label,
                    "parent": parent_id,
                    "menu_order": category.sort_index,
                }
                
                self._logger.info(f"Updating category {category.guid} in language {language.value}")
                wc_category = self._category_datasource.update(existing_wc.id, payload, language)
                existing_by_guid[wc_category.guid] = wc_category

    def _generate_unique_slug_for_language(
        self, 
        category: Category, 
        language: LanguageEnum, 
        used_slugs: set[str]
    ) -> str:
        """
        Generate a unique slug for a category in a specific language.
        """
        from slugify import slugify
        
        label = self.get_translation_for_language(category, language)
        if not label:
            # Fallback to English or first available
            label = self.get_translation_for_language(category, LanguageEnum.EN)
        
        if not label:
            raise ValueError(f"No translation found for category {category.guid}")
        
        base_slug = slugify(label)
        slug = base_slug
        i = 0
        
        while slug in used_slugs:
            self._logger.warning(f"Slug {slug} already exists for category {category.guid}, generating unique slug")
            slug = f"{base_slug}-{i}"
            i += 1
            
        return slug

    def _link_translations(
        self, 
        created_categories: Dict[LanguageEnum, WCCategory], 
        translation_links: Dict[LanguageEnum, int]
    ) -> None:
        """
        Link all translated categories together using Polylang's translation system.
        """
        if len(created_categories) <= 1:
            return  # No need to link if there's only one or no categories
        
        # Update each category with links to all other translations
        for language, wc_category in created_categories.items():
            # Create translation links excluding the current language
            other_translations = {lang: cat_id for lang, cat_id in translation_links.items() if lang != language}
            
            if other_translations:
                try:
                    self._category_datasource.update_with_translations(
                        wc_category.id, 
                        {}, 
                        other_translations
                    )
                except Exception as e:
                    self._logger.error(f"Failed to link translations for category {wc_category.id}: {e}")
