from woocommerce import API

from src.data.models.woocommerce.category import Category as WCCategory
from src.shared.enum import LanguageEnum


class WoocommerceCategoryDatasource:
    def __init__(self, woocommerce_api: API):
        self._woocommerce_api = woocommerce_api

    def get_categories(self, language: LanguageEnum | None = None) -> list[WCCategory]:
        """
        Get categories, optionally filtered by language.
        If language is None, gets categories from all languages.
        """
        categories = []
        page = 1
        params = {"per_page": 100, "page": page}
        if language:
            params["lang"] = language.value

        while True:
            response = self._woocommerce_api.get("products/categories", params=params)
            response.raise_for_status()
            data = response.json()
            categories.extend([self._map_response_category(cat) for cat in data])
            if str(page) >= response.headers.get("X-WP-TotalPages", "1"):
                break
            page += 1
            params["page"] = page
        return categories

    def create(self, payload: dict, language: LanguageEnum | None = None) -> WCCategory:
        """
        Create a category, optionally in a specific language.
        """
        params = {}
        if language:
            params["lang"] = language.value

        response = self._woocommerce_api.post("products/categories", payload, params=params)
        response.raise_for_status()
        data = response.json()
        return self._map_response_category(data)

    def update(self, category_id: int, payload: dict, language: LanguageEnum | None = None) -> WCCategory:
        """
        Update a category, optionally in a specific language.
        """
        params = {}
        if language:
            params["lang"] = language.value

        response = self._woocommerce_api.put(f"products/categories/{category_id}", payload, params=params)
        response.raise_for_status()
        data = response.json()
        return self._map_response_category(data)

    def delete(self, category_id: int) -> None:
        """
        Delete a category in WooCommerce by its ID.
        """
        response = self._woocommerce_api.delete(f"products/categories/{category_id}", params={"force": True})
        response.raise_for_status()

    def create_with_translations(self, payload: dict, translations: dict[LanguageEnum, int] | None = None) -> WCCategory:
        """
        Create a category with translation relationships.
        translations: dict mapping language codes to category IDs for linking translations
        """
        if translations:
            # Add translation links to the payload
            translation_params = {f"translations[{lang.value}]": cat_id for lang, cat_id in translations.items()}
            params = translation_params
        else:
            params = {}

        response = self._woocommerce_api.post("products/categories", payload, params=params)
        response.raise_for_status()
        data = response.json()
        return self._map_response_category(data)

    def update_with_translations(self, category_id: int, payload: dict, translations: dict[LanguageEnum, int] | None = None) -> WCCategory:
        """
        Update a category with translation relationships.
        translations: dict mapping language codes to category IDs for linking translations
        """
        if translations:
            # Add translation links to the payload
            translation_params = {f"translations[{lang.value}]": cat_id for lang, cat_id in translations.items()}
            params = translation_params
        else:
            params = {}

        response = self._woocommerce_api.put(f"products/categories/{category_id}", payload, params=params)
        response.raise_for_status()
        data = response.json()
        return self._map_response_category(data)

    def _map_response_category(self, category: dict) -> WCCategory:
        return WCCategory(
            id=category["id"],
            guid=category.get("guid") or None,
            name=category["name"],
            slug=category["slug"],
            parent_id=category["parent"],
            description=category.get("description"),
            menu_order=category.get("menu_order") or 0,
        )
