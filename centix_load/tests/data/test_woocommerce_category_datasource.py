from unittest.mock import MagicMock, <PERSON><PERSON>
from uuid import uuid4

import pytest

from src.data.models.woocommerce.category import Category as WCCategory
from src.data.woocommerce_category_datasource import WoocommerceCategoryDatasource
from src.shared.enum import LanguageEnum


@pytest.fixture
def mock_woocommerce_api():
    """Mock WooCommerce API for testing."""
    return MagicMock()


@pytest.fixture
def category_datasource(mock_woocommerce_api):
    """Create WoocommerceCategoryDatasource with mocked API."""
    return WoocommerceCategoryDatasource(mock_woocommerce_api)


@pytest.fixture
def sample_category_response():
    """Sample category response from WooCommerce API."""
    return {
        "id": 100,
        "guid": str(uuid4()),
        "name": "Test Category",
        "slug": "test-category",
        "parent": 0,
        "description": "Test description",
        "menu_order": 5
    }


@pytest.fixture
def sample_categories_response(sample_category_response):
    """Sample categories list response from WooCommerce API."""
    return [
        sample_category_response,
        {
            "id": 101,
            "guid": str(uuid4()),
            "name": "Another Category",
            "slug": "another-category",
            "parent": 100,
            "description": "Another description",
            "menu_order": 10
        }
    ]


class TestWoocommerceCategoryDatasource:
    """Test cases for WoocommerceCategoryDatasource."""

    def test_get_categories_without_language(self, category_datasource, mock_woocommerce_api, sample_categories_response):
        """Test getting categories without language filter."""
        # Setup mock response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = sample_categories_response
        mock_response.headers = {"X-WP-TotalPages": "1"}
        mock_woocommerce_api.get.return_value = mock_response
        
        result = category_datasource.get_categories()
        
        # Verify API call
        mock_woocommerce_api.get.assert_called_once_with(
            "products/categories", 
            params={"per_page": 100, "page": 1}
        )
        
        # Verify result
        assert len(result) == 2
        assert isinstance(result[0], WCCategory)
        assert result[0].id == 100
        assert result[0].name == "Test Category"

    def test_get_categories_with_language(self, category_datasource, mock_woocommerce_api, sample_categories_response):
        """Test getting categories with language filter."""
        # Setup mock response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = sample_categories_response
        mock_response.headers = {"X-WP-TotalPages": "1"}
        mock_woocommerce_api.get.return_value = mock_response
        
        result = category_datasource.get_categories(LanguageEnum.DE)
        
        # Verify API call includes language parameter
        mock_woocommerce_api.get.assert_called_once_with(
            "products/categories", 
            params={"per_page": 100, "page": 1, "lang": "de"}
        )
        
        # Verify result
        assert len(result) == 2

    def test_get_categories_pagination(self, category_datasource, mock_woocommerce_api, sample_categories_response):
        """Test getting categories with pagination."""
        # Setup mock responses for multiple pages
        mock_response_page1 = Mock()
        mock_response_page1.raise_for_status.return_value = None
        mock_response_page1.json.return_value = sample_categories_response
        mock_response_page1.headers = {"X-WP-TotalPages": "2"}
        
        mock_response_page2 = Mock()
        mock_response_page2.raise_for_status.return_value = None
        mock_response_page2.json.return_value = []
        mock_response_page2.headers = {"X-WP-TotalPages": "2"}
        
        mock_woocommerce_api.get.side_effect = [mock_response_page1, mock_response_page2]
        
        result = category_datasource.get_categories()
        
        # Verify both pages were called
        assert mock_woocommerce_api.get.call_count == 2
        mock_woocommerce_api.get.assert_any_call(
            "products/categories", 
            params={"per_page": 100, "page": 1}
        )
        mock_woocommerce_api.get.assert_any_call(
            "products/categories", 
            params={"per_page": 100, "page": 2}
        )

    def test_create_without_language(self, category_datasource, mock_woocommerce_api, sample_category_response):
        """Test creating category without language parameter."""
        # Setup mock response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = sample_category_response
        mock_woocommerce_api.post.return_value = mock_response
        
        payload = {"name": "Test Category", "slug": "test-category"}
        result = category_datasource.create(payload)
        
        # Verify API call
        mock_woocommerce_api.post.assert_called_once_with(
            "products/categories", 
            payload, 
            params={}
        )
        
        # Verify result
        assert isinstance(result, WCCategory)
        assert result.id == 100
        assert result.name == "Test Category"

    def test_create_with_language(self, category_datasource, mock_woocommerce_api, sample_category_response):
        """Test creating category with language parameter."""
        # Setup mock response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = sample_category_response
        mock_woocommerce_api.post.return_value = mock_response
        
        payload = {"name": "Test Category", "slug": "test-category"}
        result = category_datasource.create(payload, LanguageEnum.DE)
        
        # Verify API call includes language parameter
        mock_woocommerce_api.post.assert_called_once_with(
            "products/categories", 
            payload, 
            params={"lang": "de"}
        )

    def test_update_without_language(self, category_datasource, mock_woocommerce_api, sample_category_response):
        """Test updating category without language parameter."""
        # Setup mock response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = sample_category_response
        mock_woocommerce_api.put.return_value = mock_response
        
        payload = {"name": "Updated Category"}
        result = category_datasource.update(100, payload)
        
        # Verify API call
        mock_woocommerce_api.put.assert_called_once_with(
            "products/categories/100", 
            payload, 
            params={}
        )
        
        # Verify result
        assert isinstance(result, WCCategory)
        assert result.id == 100

    def test_update_with_language(self, category_datasource, mock_woocommerce_api, sample_category_response):
        """Test updating category with language parameter."""
        # Setup mock response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = sample_category_response
        mock_woocommerce_api.put.return_value = mock_response
        
        payload = {"name": "Updated Category"}
        result = category_datasource.update(100, payload, LanguageEnum.FR)
        
        # Verify API call includes language parameter
        mock_woocommerce_api.put.assert_called_once_with(
            "products/categories/100", 
            payload, 
            params={"lang": "fr"}
        )

    def test_delete(self, category_datasource, mock_woocommerce_api):
        """Test deleting category."""
        # Setup mock response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_woocommerce_api.delete.return_value = mock_response
        
        category_datasource.delete(100)
        
        # Verify API call
        mock_woocommerce_api.delete.assert_called_once_with(
            "products/categories/100", 
            params={"force": True}
        )

    def test_create_with_translations(self, category_datasource, mock_woocommerce_api, sample_category_response):
        """Test creating category with translation relationships."""
        # Setup mock response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = sample_category_response
        mock_woocommerce_api.post.return_value = mock_response
        
        payload = {"name": "Test Category", "slug": "test-category"}
        translations = {LanguageEnum.EN: 100, LanguageEnum.DE: 101}
        
        result = category_datasource.create_with_translations(payload, translations)
        
        # Verify API call includes translation parameters
        expected_params = {
            "translations[en]": 100,
            "translations[de]": 101
        }
        mock_woocommerce_api.post.assert_called_once_with(
            "products/categories", 
            payload, 
            params=expected_params
        )

    def test_create_with_translations_none(self, category_datasource, mock_woocommerce_api, sample_category_response):
        """Test creating category with no translation relationships."""
        # Setup mock response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = sample_category_response
        mock_woocommerce_api.post.return_value = mock_response
        
        payload = {"name": "Test Category", "slug": "test-category"}
        
        result = category_datasource.create_with_translations(payload, None)
        
        # Verify API call has empty params
        mock_woocommerce_api.post.assert_called_once_with(
            "products/categories", 
            payload, 
            params={}
        )

    def test_update_with_translations(self, category_datasource, mock_woocommerce_api, sample_category_response):
        """Test updating category with translation relationships."""
        # Setup mock response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = sample_category_response
        mock_woocommerce_api.put.return_value = mock_response
        
        payload = {"name": "Updated Category"}
        translations = {LanguageEnum.EN: 100, LanguageEnum.DE: 101}
        
        result = category_datasource.update_with_translations(100, payload, translations)
        
        # Verify API call includes translation parameters
        expected_params = {
            "translations[en]": 100,
            "translations[de]": 101
        }
        mock_woocommerce_api.put.assert_called_once_with(
            "products/categories/100", 
            payload, 
            params=expected_params
        )

    def test_map_response_category(self, category_datasource, sample_category_response):
        """Test mapping API response to WCCategory model."""
        result = category_datasource._map_response_category(sample_category_response)
        
        assert isinstance(result, WCCategory)
        assert result.id == 100
        assert result.name == "Test Category"
        assert result.slug == "test-category"
        assert result.parent_id == 0
        assert result.description == "Test description"
        assert result.menu_order == 5

    def test_map_response_category_minimal(self, category_datasource):
        """Test mapping minimal API response to WCCategory model."""
        minimal_response = {
            "id": 200,
            "name": "Minimal Category",
            "slug": "minimal-category",
            "parent": 0
        }
        
        result = category_datasource._map_response_category(minimal_response)
        
        assert isinstance(result, WCCategory)
        assert result.id == 200
        assert result.name == "Minimal Category"
        assert result.slug == "minimal-category"
        assert result.parent_id == 0
        assert result.description is None
        assert result.menu_order == 0  # Default value
