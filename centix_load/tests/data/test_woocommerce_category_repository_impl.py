from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest

from src.data.models.woocommerce.category import Category as WCCategory
from src.data.woocommerce_category_repository_impl import WoocommerceCategoryRepositoryImpl
from src.domain.models.proposition import Category, CategoryTranslation
from src.shared.enum import LanguageEnum


@pytest.fixture
def mock_woocommerce_api():
    """Mock WooCommerce API for testing."""
    return MagicMock()


@pytest.fixture
def mock_category_datasource():
    """Mock WoocommerceCategoryDatasource for testing."""
    return MagicMock()


@pytest.fixture
def category_repository(mock_woocommerce_api, mock_category_datasource):
    """Create WoocommerceCategoryRepositoryImpl with mocked dependencies."""
    return WoocommerceCategoryRepositoryImpl(mock_woocommerce_api, mock_category_datasource)


@pytest.fixture
def sample_category():
    """Create a sample category with multiple translations."""
    return Category(
        guid=str(uuid4()),
        id=1,
        sort_index=0,
        products=[],
        sub_categories=[],
        translations=[
            CategoryTranslation(language=LanguageEnum.EN, label="Test Category"),
            CategoryTranslation(language=LanguageEnum.DE, label="Test Kategorie"),
            CategoryTranslation(language=LanguageEnum.FR, label="Catégorie de Test"),
        ]
    )


@pytest.fixture
def sample_parent_category():
    """Create a sample parent category."""
    return Category(
        guid=str(uuid4()),
        id=2,
        sort_index=0,
        products=[],
        sub_categories=[],
        translations=[
            CategoryTranslation(language=LanguageEnum.EN, label="Parent Category"),
            CategoryTranslation(language=LanguageEnum.DE, label="Eltern Kategorie"),
        ]
    )


@pytest.fixture
def sample_wc_category():
    """Create a sample WooCommerce category."""
    return WCCategory(
        id=100,
        guid=uuid4(),
        name="Test Category",
        slug="test-category",
        parent_id=0,
        description="Test description",
        menu_order=0
    )


@pytest.fixture
def sample_categories_with_hierarchy(sample_parent_category, sample_category):
    """Create a sample category hierarchy."""
    sample_parent_category.sub_categories = [sample_category]
    return [sample_parent_category]


class TestWoocommerceCategoryRepositoryImpl:
    """Test cases for WoocommerceCategoryRepositoryImpl."""

    def test_init(self, mock_woocommerce_api, mock_category_datasource):
        """Test repository initialization."""
        repo = WoocommerceCategoryRepositoryImpl(mock_woocommerce_api, mock_category_datasource)
        
        assert repo._woocommerce_api == mock_woocommerce_api
        assert repo._category_datasource == mock_category_datasource
        assert repo._translation_service is not None

    @patch('src.data.woocommerce_category_repository_impl.PolylangTranslationService')
    def test_upsert_create_new_categories(self, mock_translation_service_class, category_repository, sample_category, mock_category_datasource):
        """Test upserting new categories (create scenario)."""
        # Setup mocks
        mock_translation_service = MagicMock()
        mock_translation_service_class.return_value = mock_translation_service
        
        mock_category_datasource.get_categories.return_value = []
        
        # Mock created categories
        created_categories = {
            LanguageEnum.EN: WCCategory(id=100, guid=uuid4(), name="Test Category", slug="test-category", parent_id=0, menu_order=0),
            LanguageEnum.DE: WCCategory(id=101, guid=uuid4(), name="Test Kategorie", slug="test-kategorie", parent_id=0, menu_order=0),
        }
        mock_translation_service.create_translated_categories.return_value = created_categories
        
        # Create new repository instance to use the mocked translation service
        repo = WoocommerceCategoryRepositoryImpl(MagicMock(), mock_category_datasource)
        repo._translation_service = mock_translation_service
        
        # Execute
        repo.upsert([sample_category])
        
        # Verify
        mock_translation_service.create_translated_categories.assert_called_once()
        mock_category_datasource.delete.assert_not_called()  # No categories to delete

    @patch('src.data.woocommerce_category_repository_impl.PolylangTranslationService')
    def test_upsert_update_existing_categories(self, mock_translation_service_class, category_repository, sample_category, sample_wc_category, mock_category_datasource):
        """Test upserting existing categories (update scenario)."""
        # Setup mocks
        mock_translation_service = MagicMock()
        mock_translation_service_class.return_value = mock_translation_service
        
        sample_wc_category.guid = sample_category.guid
        mock_category_datasource.get_categories.return_value = [sample_wc_category]
        
        # Create new repository instance to use the mocked translation service
        repo = WoocommerceCategoryRepositoryImpl(MagicMock(), mock_category_datasource)
        repo._translation_service = mock_translation_service
        
        # Execute
        repo.upsert([sample_category])
        
        # Verify
        mock_translation_service.update_translated_categories.assert_called_once()
        mock_category_datasource.delete.assert_not_called()  # No categories to delete

    @patch('src.data.woocommerce_category_repository_impl.PolylangTranslationService')
    def test_upsert_delete_orphaned_categories(self, mock_translation_service_class, category_repository, sample_category, mock_category_datasource):
        """Test upserting with deletion of orphaned categories."""
        # Setup mocks
        mock_translation_service = MagicMock()
        mock_translation_service_class.return_value = mock_translation_service
        
        # Create orphaned category (not in upsert list)
        orphaned_category = WCCategory(
            id=200,
            guid=uuid4(),
            name="Orphaned Category",
            slug="orphaned-category",
            parent_id=0,
            menu_order=0
        )
        
        mock_category_datasource.get_categories.return_value = [orphaned_category]
        
        created_categories = {
            LanguageEnum.EN: WCCategory(id=100, guid=uuid4(), name="Test Category", slug="test-category", parent_id=0, menu_order=0)
        }
        mock_translation_service.create_translated_categories.return_value = created_categories
        
        # Create new repository instance to use the mocked translation service
        repo = WoocommerceCategoryRepositoryImpl(MagicMock(), mock_category_datasource)
        repo._translation_service = mock_translation_service
        
        # Execute
        repo.upsert([sample_category])
        
        # Verify orphaned category is deleted
        mock_category_datasource.delete.assert_called_once_with(200)

    def test_upsert_skip_default_uncategorized(self, category_repository, sample_category, mock_category_datasource):
        """Test that default 'Uncategorized' category is not deleted."""
        # Setup default uncategorized category
        uncategorized_category = WCCategory(
            id=1,
            guid=None,
            name="Uncategorized",
            slug="uncategorized",
            parent_id=0,
            menu_order=0
        )
        
        mock_category_datasource.get_categories.return_value = [uncategorized_category]
        
        # Mock translation service
        with patch.object(category_repository._translation_service, 'create_translated_categories') as mock_create:
            created_categories = {
                LanguageEnum.EN: WCCategory(id=100, guid=uuid4(), name="Test Category", slug="test-category", parent_id=0, menu_order=0)
            }
            mock_create.return_value = created_categories
            
            # Execute
            category_repository.upsert([sample_category])
        
        # Verify uncategorized category is NOT deleted
        mock_category_datasource.delete.assert_not_called()

    def test_flatten_categories_simple(self, category_repository, sample_category):
        """Test flattening simple category list."""
        result = category_repository._flatten_categories([sample_category])
        
        assert len(result) == 1
        assert result[0] == (sample_category, None)

    def test_flatten_categories_with_hierarchy(self, category_repository, sample_categories_with_hierarchy):
        """Test flattening category hierarchy."""
        result = category_repository._flatten_categories(sample_categories_with_hierarchy)
        
        assert len(result) == 2
        # Parent category comes first
        assert result[0][0] == sample_categories_with_hierarchy[0]  # Parent
        assert result[0][1] is None  # No parent for root
        # Child category comes second
        assert result[1][0] == sample_categories_with_hierarchy[0].sub_categories[0]  # Child
        assert result[1][1] == sample_categories_with_hierarchy[0]  # Parent reference

    def test_build_used_slugs_by_parent(self, category_repository):
        """Test building used slugs mapping by parent."""
        categories = [
            WCCategory(id=1, guid=uuid4(), name="Cat1", slug="cat1", parent_id=0, menu_order=0),
            WCCategory(id=2, guid=uuid4(), name="Cat2", slug="cat2", parent_id=0, menu_order=0),
            WCCategory(id=3, guid=uuid4(), name="Cat3", slug="cat3", parent_id=1, menu_order=0),
        ]
        
        result = category_repository._build_used_slugs_by_parent(categories)
        
        assert result[0] == {"cat1", "cat2"}  # Parent 0 has cat1, cat2
        assert result[1] == {"cat3"}  # Parent 1 has cat3

    def test_get_parent_id_with_parent(self, category_repository, sample_wc_category):
        """Test getting parent ID when parent exists."""
        parent_guid = str(sample_wc_category.guid)
        existing_by_guid = {sample_wc_category.guid: sample_wc_category}
        
        result = category_repository._get_parent_id(parent_guid, existing_by_guid)
        
        assert result == sample_wc_category.id

    def test_get_parent_id_without_parent(self, category_repository):
        """Test getting parent ID when no parent."""
        result = category_repository._get_parent_id(None, {})
        
        assert result == 0

    def test_get_parent_id_parent_not_found(self, category_repository):
        """Test getting parent ID when parent GUID not found."""
        parent_guid = str(uuid4())
        existing_by_guid = {}
        
        result = category_repository._get_parent_id(parent_guid, existing_by_guid)
        
        assert result == 0

    def test_get_english_label_exists(self, category_repository, sample_category):
        """Test getting English label when it exists."""
        result = category_repository._get_english_label(sample_category)
        
        assert result == "Test Category"

    def test_get_english_label_not_exists(self, category_repository):
        """Test getting English label when it doesn't exist."""
        category_no_english = Category(
            guid=str(uuid4()),
            id=1,
            sort_index=0,
            products=[],
            sub_categories=[],
            translations=[
                CategoryTranslation(language=LanguageEnum.DE, label="Nur Deutsch"),
            ]
        )
        
        with pytest.raises(ValueError, match="No English translation found"):
            category_repository._get_english_label(category_no_english)

    @patch('src.data.woocommerce_category_repository_impl.slugify')
    def test_generate_slug(self, mock_slugify, category_repository, sample_category):
        """Test generating slug from English label."""
        mock_slugify.return_value = "test-category"
        
        result = category_repository._generate_slug(sample_category)
        
        assert result == "test-category"
        mock_slugify.assert_called_once_with("Test Category")

    @patch('src.data.woocommerce_category_repository_impl.slugify')
    def test_generate_unique_slug_no_collision(self, mock_slugify, category_repository, sample_category):
        """Test generating unique slug when no collision."""
        mock_slugify.return_value = "test-category"
        used_slugs = set()
        
        result = category_repository._generate_unique_slug(sample_category, used_slugs)
        
        assert result == "test-category"

    @patch('src.data.woocommerce_category_repository_impl.slugify')
    def test_generate_unique_slug_with_collision(self, mock_slugify, category_repository, sample_category):
        """Test generating unique slug when collision occurs."""
        mock_slugify.return_value = "test-category"
        used_slugs = {"test-category", "test-category-0"}

        result = category_repository._generate_unique_slug(sample_category, used_slugs)

        assert result == "test-category-1"


class TestWoocommerceCategoryRepositoryImplIntegration:
    """Integration tests for WoocommerceCategoryRepositoryImpl with translation service."""

    @pytest.fixture
    def integration_repository(self, mock_woocommerce_api, mock_category_datasource):
        """Create repository for integration testing."""
        return WoocommerceCategoryRepositoryImpl(mock_woocommerce_api, mock_category_datasource)

    def test_create_category_with_translations_integration(self, integration_repository, sample_category, mock_category_datasource):
        """Integration test for creating category with all translations."""
        # Setup mock responses for each language
        mock_wc_categories = {}
        for i, lang in enumerate([LanguageEnum.EN, LanguageEnum.DE, LanguageEnum.FR]):
            mock_wc_category = WCCategory(
                id=100 + i,
                guid=sample_category.guid,
                name=f"Category {lang.value}",
                slug=f"category-{lang.value}",
                parent_id=0,
                menu_order=0
            )
            mock_wc_categories[lang] = mock_wc_category

        # Mock datasource methods
        mock_category_datasource.get_categories.return_value = []
        mock_category_datasource.create.side_effect = lambda payload, language: mock_wc_categories[language]
        mock_category_datasource.update_with_translations.return_value = None

        # Execute
        integration_repository.upsert([sample_category])

        # Verify create was called for each language
        assert mock_category_datasource.create.call_count == 3

        # Verify translation linking was attempted
        assert mock_category_datasource.update_with_translations.call_count == 3

    def test_update_category_with_translations_integration(self, integration_repository, sample_category, mock_category_datasource):
        """Integration test for updating category with all translations."""
        # Setup existing category
        existing_wc_category = WCCategory(
            id=100,
            guid=sample_category.guid,
            name="Old Name",
            slug="old-name",
            parent_id=0,
            menu_order=0
        )

        # Mock datasource methods
        mock_category_datasource.get_categories.return_value = [existing_wc_category]
        mock_category_datasource.update.return_value = existing_wc_category

        # Execute
        integration_repository.upsert([sample_category])

        # Verify update was called for each available language
        assert mock_category_datasource.update.call_count == 3

    def test_mixed_create_update_scenario(self, integration_repository, sample_category, sample_parent_category, mock_category_datasource):
        """Integration test for mixed create/update scenario."""
        # Setup: parent exists, child is new
        existing_parent = WCCategory(
            id=200,
            guid=sample_parent_category.guid,
            name="Existing Parent",
            slug="existing-parent",
            parent_id=0,
            menu_order=0
        )

        # Mock new child categories
        mock_child_categories = {}
        for i, lang in enumerate([LanguageEnum.EN, LanguageEnum.DE, LanguageEnum.FR]):
            mock_child_categories[lang] = WCCategory(
                id=300 + i,
                guid=sample_category.guid,
                name=f"Child {lang.value}",
                slug=f"child-{lang.value}",
                parent_id=200,
                menu_order=0
            )

        # Setup mocks
        mock_category_datasource.get_categories.return_value = [existing_parent]
        mock_category_datasource.update.return_value = existing_parent
        mock_category_datasource.create.side_effect = lambda payload, language: mock_child_categories[language]
        mock_category_datasource.update_with_translations.return_value = None

        # Create hierarchy
        sample_parent_category.sub_categories = [sample_category]

        # Execute
        integration_repository.upsert([sample_parent_category])

        # Verify parent was updated (for each language)
        assert mock_category_datasource.update.call_count >= 2  # At least for available languages

        # Verify child was created (for each language)
        assert mock_category_datasource.create.call_count == 3

    def test_fallback_translation_behavior(self, integration_repository, mock_category_datasource):
        """Test fallback behavior when some translations are missing."""
        # Create category with only English and German
        category_partial_translations = Category(
            guid=str(uuid4()),
            id=1,
            sort_index=0,
            products=[],
            sub_categories=[],
            translations=[
                CategoryTranslation(language=LanguageEnum.EN, label="English Only"),
                CategoryTranslation(language=LanguageEnum.DE, label="Deutsch Auch"),
            ]
        )

        # Mock responses
        mock_wc_categories = {
            LanguageEnum.EN: WCCategory(id=100, guid=category_partial_translations.guid, name="English Only", slug="english-only", parent_id=0, menu_order=0),
            LanguageEnum.DE: WCCategory(id=101, guid=category_partial_translations.guid, name="Deutsch Auch", slug="deutsch-auch", parent_id=0, menu_order=0),
        }

        mock_category_datasource.get_categories.return_value = []
        mock_category_datasource.create.side_effect = lambda payload, language: mock_wc_categories[language]
        mock_category_datasource.update_with_translations.return_value = None

        # Execute
        integration_repository.upsert([category_partial_translations])

        # Verify only available languages were created
        assert mock_category_datasource.create.call_count == 2
