from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest

from src.data.models.woocommerce.category import Category as WCCategory
from src.data.services.polylang_translation_service import PolylangTranslationService
from src.domain.models.proposition import Category, CategoryTranslation
from src.shared.enum import LanguageEnum


@pytest.fixture
def mock_category_datasource():
    """Mock WoocommerceCategoryDatasource for testing."""
    return MagicMock()


@pytest.fixture
def translation_service(mock_category_datasource):
    """Create PolylangTranslationService with mocked datasource."""
    return PolylangTranslationService(mock_category_datasource)


@pytest.fixture
def sample_category():
    """Create a sample category with multiple translations."""
    return Category(
        guid=str(uuid4()),
        id=1,
        sort_index=0,
        products=[],
        sub_categories=[],
        translations=[
            CategoryTranslation(language=LanguageEnum.EN, label="Test Category"),
            CategoryTranslation(language=LanguageEnum.DE, label="Test Kategorie"),
            CategoryTranslation(language=LanguageEnum.FR, label="Catégorie de Test"),
            CategoryTranslation(language=LanguageEnum.NL, label="Test Categorie"),
        ]
    )


@pytest.fixture
def sample_category_english_only():
    """Create a sample category with only English translation."""
    return Category(
        guid=str(uuid4()),
        id=2,
        sort_index=1,
        products=[],
        sub_categories=[],
        translations=[
            CategoryTranslation(language=LanguageEnum.EN, label="English Only Category"),
        ]
    )


@pytest.fixture
def sample_wc_category():
    """Create a sample WooCommerce category."""
    return WCCategory(
        id=100,
        guid=uuid4(),
        name="Test Category",
        slug="test-category",
        parent_id=0,
        description="Test description",
        menu_order=0
    )


class TestPolylangTranslationService:
    """Test cases for PolylangTranslationService."""

    def test_get_translation_for_language_existing(self, translation_service, sample_category):
        """Test getting translation for an existing language."""
        result = translation_service.get_translation_for_language(sample_category, LanguageEnum.DE)
        assert result == "Test Kategorie"

    def test_get_translation_for_language_fallback_to_english(self, translation_service, sample_category):
        """Test fallback to English when requested language doesn't exist."""
        result = translation_service.get_translation_for_language(sample_category, LanguageEnum.ES)
        assert result == "Test Category"  # Falls back to English

    def test_get_translation_for_language_fallback_to_first(self, translation_service):
        """Test fallback to first available translation when English doesn't exist."""
        category_no_english = Category(
            guid=str(uuid4()),
            id=3,
            sort_index=0,
            products=[],
            sub_categories=[],
            translations=[
                CategoryTranslation(language=LanguageEnum.DE, label="Nur Deutsch"),
                CategoryTranslation(language=LanguageEnum.FR, label="Seulement Français"),
            ]
        )
        
        result = translation_service.get_translation_for_language(category_no_english, LanguageEnum.ES)
        assert result == "Nur Deutsch"  # Falls back to first available

    def test_get_translation_for_language_no_translations(self, translation_service):
        """Test behavior when category has no translations."""
        category_no_translations = Category(
            guid=str(uuid4()),
            id=4,
            sort_index=0,
            products=[],
            sub_categories=[],
            translations=[]
        )
        
        result = translation_service.get_translation_for_language(category_no_translations, LanguageEnum.EN)
        assert result is None

    def test_get_available_languages(self, translation_service, sample_category):
        """Test getting all available languages for a category."""
        result = translation_service.get_available_languages(sample_category)
        expected = [LanguageEnum.EN, LanguageEnum.DE, LanguageEnum.FR, LanguageEnum.NL]
        assert result == expected

    def test_get_available_languages_empty(self, translation_service):
        """Test getting available languages for category with no translations."""
        category_no_translations = Category(
            guid=str(uuid4()),
            id=5,
            sort_index=0,
            products=[],
            sub_categories=[],
            translations=[]
        )
        
        result = translation_service.get_available_languages(category_no_translations)
        assert result == []

    @patch('src.data.services.polylang_translation_service.slugify')
    def test_generate_unique_slug_for_language(self, mock_slugify, translation_service, sample_category):
        """Test generating unique slug for a specific language."""
        mock_slugify.return_value = "test-category"
        used_slugs = set()
        
        result = translation_service._generate_unique_slug_for_language(
            sample_category, LanguageEnum.EN, used_slugs
        )
        
        assert result == "test-category"
        mock_slugify.assert_called_once_with("Test Category")

    @patch('src.data.services.polylang_translation_service.slugify')
    def test_generate_unique_slug_for_language_collision(self, mock_slugify, translation_service, sample_category):
        """Test generating unique slug when collision occurs."""
        mock_slugify.return_value = "test-category"
        used_slugs = {"test-category", "test-category-0"}
        
        result = translation_service._generate_unique_slug_for_language(
            sample_category, LanguageEnum.EN, used_slugs
        )
        
        assert result == "test-category-1"

    def test_create_translated_categories(self, translation_service, sample_category, mock_category_datasource):
        """Test creating translated categories for all available languages."""
        # Setup mock responses
        mock_wc_categories = {}
        for i, lang in enumerate([LanguageEnum.EN, LanguageEnum.DE, LanguageEnum.FR, LanguageEnum.NL]):
            mock_wc_category = WCCategory(
                id=100 + i,
                guid=uuid4(),
                name=f"Category {lang.value}",
                slug=f"category-{lang.value}",
                parent_id=0,
                description="",
                menu_order=0
            )
            mock_wc_categories[lang] = mock_wc_category
        
        mock_category_datasource.create.side_effect = lambda payload, language: mock_wc_categories[language]
        
        used_slugs_by_parent = {0: set()}
        existing_by_guid = {}
        
        result = translation_service.create_translated_categories(
            sample_category, 0, used_slugs_by_parent, existing_by_guid
        )
        
        # Verify all languages were created
        assert len(result) == 4
        assert LanguageEnum.EN in result
        assert LanguageEnum.DE in result
        assert LanguageEnum.FR in result
        assert LanguageEnum.NL in result
        
        # Verify datasource.create was called for each language
        assert mock_category_datasource.create.call_count == 4

    def test_create_translated_categories_english_only(self, translation_service, sample_category_english_only, mock_category_datasource):
        """Test creating translated categories when only English is available."""
        mock_wc_category = WCCategory(
            id=100,
            guid=uuid4(),
            name="English Only Category",
            slug="english-only-category",
            parent_id=0,
            description="",
            menu_order=0
        )
        
        mock_category_datasource.create.return_value = mock_wc_category
        
        used_slugs_by_parent = {0: set()}
        existing_by_guid = {}
        
        result = translation_service.create_translated_categories(
            sample_category_english_only, 0, used_slugs_by_parent, existing_by_guid
        )
        
        # Verify only English was created
        assert len(result) == 1
        assert LanguageEnum.EN in result
        assert mock_category_datasource.create.call_count == 1

    def test_update_translated_categories(self, translation_service, sample_category, sample_wc_category, mock_category_datasource):
        """Test updating translated categories."""
        mock_category_datasource.update.return_value = sample_wc_category
        existing_by_guid = {}
        
        translation_service.update_translated_categories(
            sample_wc_category, sample_category, 0, existing_by_guid
        )
        
        # Verify update was called for each available language
        assert mock_category_datasource.update.call_count == 4

    def test_link_translations_single_category(self, translation_service):
        """Test linking translations when there's only one category (should not link)."""
        created_categories = {
            LanguageEnum.EN: WCCategory(id=100, guid=uuid4(), name="Test", slug="test", parent_id=0, menu_order=0)
        }
        translation_links = {LanguageEnum.EN: 100}
        
        # Should not raise any errors and not call update_with_translations
        translation_service._link_translations(created_categories, translation_links)

    def test_link_translations_multiple_categories(self, translation_service, mock_category_datasource):
        """Test linking translations when there are multiple categories."""
        created_categories = {
            LanguageEnum.EN: WCCategory(id=100, guid=uuid4(), name="Test EN", slug="test-en", parent_id=0, menu_order=0),
            LanguageEnum.DE: WCCategory(id=101, guid=uuid4(), name="Test DE", slug="test-de", parent_id=0, menu_order=0),
        }
        translation_links = {LanguageEnum.EN: 100, LanguageEnum.DE: 101}
        
        translation_service._link_translations(created_categories, translation_links)
        
        # Verify update_with_translations was called for each category
        assert mock_category_datasource.update_with_translations.call_count == 2
